# Define common permissions that all roles should have
# Note: Salary permissions are handled separately in 08_salary_permissions.rb
common_permissions = Permission.where(action: %w[read_own update_own], subject_class: "Employee", system_name: "people")
                              .or(Permission.where(action: "read", subject_class: "Role"))
                              .or(Permission.where(action: "read", subject_class: "Project"))
                              .or(Permission.where(action: "read_own", subject_class: "User", system_name: "core"))
                              .or(Permission.where(action: %w[create submit withdraw read_own manage_own], subject_class: "Leave", system_name: "people"))
                              .or(Permission.where(action: %w[record read_own], subject_class: "AttendanceEvent", system_name: "people"))
                              .or(Permission.where(action: %w[read_own cancel_own], subject_class: "ApprovalRequest", system_name: "people"))

# Define common permissions for CM-enabled roles (excludes project_manager)
cm_common_permissions = Permission.where(action: %w[read_own cancel_own], subject_class: "ApprovalRequest", system_name: "cm")

# Assign permissions to roles
role_permission_map = {
  "super_admin" => Permission.all,
  "admin" => Permission.all.where.not(action: %w[manage cancel_own read_own update_own]),
  "hr_manager" => Permission.where(action: %w[read create update], subject_class: "Employee")
                            .or(Permission.where(subject_class: "Leave"))
                            .or(Permission.where(subject_class: "AttendanceEvent"))
                            .or(Permission.where(subject_class: "AttendanceDevice"))
                            .or(Permission.where(subject_class: "AttendanceSyncLog"))
                            .or(Permission.where(subject_class: "AttendanceExemption"))
                            .or(Permission.where(action: %w[read update create], subject_class: "User"))
                            .or(Permission.where(subject_class: "setting"))
                            .or(Permission.where(subject_class: "ApprovalRequest", system_name: "people")),
  "hr_officer" => Permission.where(system_name: "people", action: %w[read update create approve reject])
                            .or(Permission.where(action: %w[read], subject_class: "User"))
                            .or(Permission.where(action: %w[read create update approve reject], subject_class: "AttendanceEvent"))
                            .or(Permission.where(action: %w[read sync test_connection execute_command], subject_class: "AttendanceDevice"))
                            .or(Permission.where(action: %w[read], subject_class: "AttendanceSyncLog"))
                            .or(Permission.where(action: %w[read], subject_class: "setting"))
                            .or(Permission.where(subject_class: "ApprovalRequest", system_name: "people", action: %w[read approve reject])),
  "procurement_manager" => Permission.where(system_name: "procure").or(Permission.where(action: %w[read], subject_class: "User"))
                                     .or(Permission.where(subject_class: "ApprovalRequest", system_name: "procure")),
  "procurement_officer" => Permission.where(system_name: "procure", action: %w[read create update]).or(Permission.where(action: %w[read], subject_class: "User"))
                                     .or(Permission.where(subject_class: "ApprovalRequest", system_name: "procure", action: %w[read approve reject])),
  "financial_manager" => Permission.where(subject_class: "Payroll").or(Permission.where(action: %w[read], subject_class: "User"))
                                   .or(Permission.where(subject_class: "ApprovalRequest")),
  "accountant" => Permission.where(subject_class: "Payroll", action: %w[read update]).or(Permission.where(action: %w[read], subject_class: "User"))
                            .or(Permission.where(subject_class: "ApprovalRequest", action: %w[read approve reject])),
  "case_manager" => [
    # TODO: Fix own logic use the prebuilt auth logic
    # Case management - own cases only
    Permission.where(action: %w[read create update submit_for_approval], subject_class: "Case", system_name: "cm"),

    # Form management - own cases only
    Permission.where(action: %w[read], subject_class: "FormTemplate", system_name: "cm"),
    Permission.where(action: %w[read], subject_class: "FormSection", system_name: "cm"),
    Permission.where(action: %w[read create update submit calculate_fields], subject_class: "FormSubmission", system_name: "cm"),

    # Comments - own cases only
    Permission.where(action: %w[read create update resolve reopen], subject_class: "Comment", system_name: "cm"),

    # Documents - own cases only
    Permission.where(action: %w[read create update download preview], subject_class: "CaseDocument", system_name: "cm"),

    # Basic reporting
    Permission.where(action: %w[read], subject_class: %w[Dashboard Charts], system_name: "cm"),
    Permission.where(action: %w[read], subject_class: "Reports", system_name: "cm"),

    # Own HR data only (AtharPeople integration)
    Permission.where(action: %w[read_own update_own], subject_class: "Employee", system_name: "people"),
    Permission.where(action: %w[read_own manage_own], subject_class: "Leave", system_name: "people"),
    Permission.where(action: %w[read_own record], subject_class: "AttendanceEvent", system_name: "people"),

    # Legacy permissions
    Permission.where(action: %w[manage], subject_class: "Beneficiary", system_name: "cm"),
    Permission.where(action: %w[read create update], subject_class: "Service", system_name: "cm"),

    # Basic user and approval permissions
    Permission.where(action: %w[read], subject_class: "User"),
    Permission.where(subject_class: "ApprovalRequest", system_name: "cm", action: %w[read_own cancel_own]),

    # Procurement permissions - case managers can create and manage their own procurement requests
    Permission.where(action: %w[create read_own update_own], subject_class: "ProcurementRequest", system_name: "procure"),
    Permission.where(action: %w[read_own cancel_own], subject_class: "ApprovalRequest", system_name: "procure")
  ].flatten,
  "project_manager" => [
    # NO ACCESS to AtharCM system - Project Managers are completely excluded
    # HR management only through AtharPeople system
    Permission.where(action: %w[read_project update_project manage_project], subject_class: "Employee", system_name: "people"),
    Permission.where(action: %w[read_project approve_project reject_project], subject_class: "Leave", system_name: "people"),
    Permission.where(action: %w[read_project update_project], subject_class: "AttendanceEvent", system_name: "people"),
    Permission.where(action: %w[read], subject_class: "User"),
    Permission.where(subject_class: "ApprovalRequest", system_name: "people", action: %w[read approve reject]),
    # Own HR data
    Permission.where(action: %w[read_own update_own], subject_class: "Employee", system_name: "people"),
    Permission.where(action: %w[read_own manage_own], subject_class: "Leave", system_name: "people"),
    Permission.where(action: %w[read_own record], subject_class: "AttendanceEvent", system_name: "people"),
    # Own approval requests for people system only (NO CM approval access)
    Permission.where(action: %w[read_own cancel_own], subject_class: "ApprovalRequest", system_name: "people")
  ].flatten,
  "supervisor" => [
    # Full case management within project
    Permission.where(action: %w[read create update destroy submit_for_approval approve reject assign], subject_class: "Case", system_name: "cm"),

    # Form management - project-wide + template management
    Permission.where(action: %w[read create update], subject_class: "FormTemplate", system_name: "cm"),
    Permission.where(action: %w[read create update], subject_class: "FormSection", system_name: "cm"),
    Permission.where(action: %w[read create update submit calculate_fields], subject_class: "FormSubmission", system_name: "cm"),

    # Comments - project-wide
    Permission.where(action: %w[read create update destroy resolve reopen], subject_class: "Comment", system_name: "cm"),

    # Documents - project-wide
    Permission.where(action: %w[read create update destroy download preview], subject_class: "CaseDocument", system_name: "cm"),

    # Advanced reporting and analytics
    Permission.where(action: %w[read], subject_class: %w[Dashboard Charts TeamMetrics Analytics], system_name: "cm"),
    Permission.where(action: %w[read create export], subject_class: "Reports", system_name: "cm"),

    # Own HR data only (AtharPeople integration)
    Permission.where(action: %w[read_own update_own], subject_class: "Employee", system_name: "people"),
    Permission.where(action: %w[read_own manage_own], subject_class: "Leave", system_name: "people"),
    Permission.where(action: %w[read_own record], subject_class: "AttendanceEvent", system_name: "people"),

    # Legacy permissions
    Permission.where(action: %w[manage], subject_class: "Beneficiary", system_name: "cm"),
    Permission.where(action: %w[read create update], subject_class: "Service", system_name: "cm"),

    # Basic user and approval permissions
    Permission.where(action: %w[read], subject_class: "User"),
    Permission.where(subject_class: "ApprovalRequest", system_name: "cm", action: %w[read approve reject])
  ].flatten,
  "psychologist" => [
    # Same permissions as case_manager - own cases only
    Permission.where(action: %w[read create update submit_for_approval], subject_class: "Case", system_name: "cm"),
    Permission.where(action: %w[read], subject_class: "FormTemplate", system_name: "cm"),
    Permission.where(action: %w[read], subject_class: "FormSection", system_name: "cm"),
    Permission.where(action: %w[read create update submit calculate_fields], subject_class: "FormSubmission", system_name: "cm"),
    Permission.where(action: %w[read create update resolve reopen], subject_class: "Comment", system_name: "cm"),
    Permission.where(action: %w[read create update download preview], subject_class: "CaseDocument", system_name: "cm"),
    Permission.where(action: %w[read], subject_class: %w[Dashboard Charts], system_name: "cm"),
    Permission.where(action: %w[read], subject_class: "Reports", system_name: "cm"),
    Permission.where(action: %w[read_own update_own], subject_class: "Employee", system_name: "people"),
    Permission.where(action: %w[read_own manage_own], subject_class: "Leave", system_name: "people"),
    Permission.where(action: %w[read_own record], subject_class: "AttendanceEvent", system_name: "people"),
    Permission.where(action: %w[manage], subject_class: "Beneficiary", system_name: "cm"),
    Permission.where(action: %w[read create update], subject_class: "Service", system_name: "cm"),
    Permission.where(action: %w[read], subject_class: "User"),
    Permission.where(subject_class: "ApprovalRequest", system_name: "cm", action: %w[read_own cancel_own]),

    # Procurement permissions - psychologists can create and manage their own procurement requests
    Permission.where(action: %w[create read_own update_own], subject_class: "ProcurementRequest", system_name: "procure"),
    Permission.where(action: %w[read_own cancel_own], subject_class: "ApprovalRequest", system_name: "procure")
  ].flatten,
  "social_media_specialist" => Permission.where(action: %w[read], subject_class: "User")
                                         .or(Permission.where(action: %w[manage_own], subject_class: "User", system_name: "core"))
                                         .or(Permission.where(action: %w[create submit withdraw read_own manage_own], subject_class: "Leave", system_name: "people"))
                                         .or(Permission.where(action: %w[record read_own], subject_class: "AttendanceEvent", system_name: "people"))
                                         .or(Permission.where(action: %w[read_own cancel_own], subject_class: "ApprovalRequest"))
                                         # Procurement permissions - social media specialists can create and manage their own procurement requests
                                         .or(Permission.where(action: %w[create read_own update_own], subject_class: "ProcurementRequest", system_name: "procure"))
                                         .or(Permission.where(action: %w[read_own cancel_own], subject_class: "ApprovalRequest", system_name: "procure")),
  "employee" => Permission.where(action: %w[read], subject_class: "User")
                          .or(Permission.where(action: %w[manage_own], subject_class: "User", system_name: "core"))
                          .or(Permission.where(action: %w[create submit withdraw read_own manage_own], subject_class: "Leave", system_name: "people"))
                          .or(Permission.where(action: %w[record read_own], subject_class: "AttendanceEvent", system_name: "people"))
                          .or(Permission.where(action: %w[read_own cancel_own], subject_class: "ApprovalRequest"))
                          # Procurement permissions - employees can create and manage their own procurement requests
                          .or(Permission.where(action: %w[create read_own update_own], subject_class: "ProcurementRequest", system_name: "procure"))
                          .or(Permission.where(action: %w[read_own cancel_own], subject_class: "ApprovalRequest", system_name: "procure"))
}

# Define basic employee permissions for roles that should inherit employee permissions
basic_employee_permissions = Permission.where(action: %w[read], subject_class: "User")
                                       .or(Permission.where(action: %w[manage_own], subject_class: "User", system_name: "core"))
                                       .or(Permission.where(action: %w[create submit withdraw read_own manage_own], subject_class: "Leave", system_name: "people"))
                                       .or(Permission.where(action: %w[record read_own], subject_class: "AttendanceEvent", system_name: "people"))
                                       .or(Permission.where(action: %w[read_own cancel_own], subject_class: "ApprovalRequest"))
                                       # Procurement permissions - employees can create and manage their own procurement requests
                                       .or(Permission.where(action: %w[create read_own update_own], subject_class: "ProcurementRequest", system_name: "procure"))
                                       .or(Permission.where(action: %w[read_own cancel_own], subject_class: "ApprovalRequest", system_name: "procure"))

# Define project-based employee permissions for roles that should see employees in their projects
project_employee_permissions = Permission.where(action: %w[read_project update_project], subject_class: "Employee", system_name: "people")
                                         .or(Permission.where(action: %w[read_project approve_project reject_project], subject_class: "Leave", system_name: "people"))
                                         .or(Permission.where(action: %w[read_project], subject_class: "AttendanceEvent", system_name: "people"))
                                         .or(basic_employee_permissions)

# Roles with basic employee permissions (no employee list access)
basic_employee_roles = %w[
  coach
  graphic_designer
  community_mobilizer
  club_facilitator
  community_animator
  housekeeping
  fundraising_officer
  volunteer
]

# Roles with project-based employee permissions (can see employees in their projects)
project_employee_roles = %w[
  program_manager
]

# Add basic employee permissions to the role permission map
basic_employee_roles.each do |role_name|
  role_permission_map[role_name] = basic_employee_permissions
end

# Add project-based employee permissions to the role permission map
project_employee_roles.each do |role_name|
  role_permission_map[role_name] = project_employee_permissions
end

role_permission_map.each do |role_name, perms|
  role = Role.find_by!(name: role_name) # Fail if role doesn't exist

  # Add role-specific permissions
  perms.each do |permission|
    unless role.permissions.exists?(permission.id)
      role.permissions << permission
    end
  end

  # Add common permissions to all roles except super_admin and admin (who already have all permissions)
  unless %w[super_admin admin].include?(role_name)
    common_permissions.each do |permission|
      unless role.permissions.exists?(permission.id)
        role.permissions << permission
      end
    end

    # Add CM approval permissions to CM-enabled roles only (excludes project_manager)
    unless %w[project_manager].include?(role_name)
      cm_common_permissions.each do |permission|
        unless role.permissions.exists?(permission.id)
          role.permissions << permission
        end
      end
    end
  end
end

puts "✅ Role permissions mapped successfully!"
